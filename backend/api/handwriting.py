from fastapi import APIRouter, UploadFile, File, HTTPException
from fastapi.responses import FileResponse
from ..models.handwriting_model import HandwritingModel
from ..processors.image_processor import ImageProcessor
from ..utils.file_utils import save_upload_file

router = APIRouter()

handwriting_model = HandwritingModel()
image_processor = ImageProcessor()

@router.post("/upload-samples")
async def upload_samples(
    printed_text: UploadFile = File(...),
    handwritten_text: UploadFile = File(...)
):
    try:
        printed_path = await save_upload_file(printed_text, "printed")
        handwritten_path = await save_upload_file(handwritten_text, "handwritten")
        
        processed_data = image_processor.process_samples(
            printed_path, handwritten_path
        )
        
        model_path = handwriting_model.train(processed_data)
        
        return {
            "message": "Samples uploaded successfully",
            "model_path": model_path
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

from pydantic import BaseModel

class GenerateRequest(BaseModel):
    text: str
    output_format: str = "pdf"

@router.post("/generate")
async def generate_handwriting(request: GenerateRequest):
    try:
        result = handwriting_model.generate(request.text)

        if request.output_format == "pdf":
            output_path = handwriting_model.export_pdf(result)
        elif request.output_format == "word":
            output_path = handwriting_model.export_word(result)
        elif request.output_format == "image":
            output_path = handwriting_model.export_image(result)
        elif request.output_format == "gcode":
            output_path = handwriting_model.export_gcode(result)
        else:
            raise HTTPException(status_code=400, detail="Unsupported output format")
        
        return FileResponse(output_path)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/models")
async def list_models():
    models = handwriting_model.list_available_models()
    return {"models": models}
