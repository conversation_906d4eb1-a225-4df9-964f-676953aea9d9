from fastapi import APIRouter, UploadFile, File, HTTPException
from fastapi.responses import FileResponse
import os
from typing import List
from ..utils.file_utils import save_upload_file, get_file_info

router = APIRouter()

@router.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    try:
        file_path = await save_upload_file(file)
        file_info = get_file_info(file_path)
        return {
            "message": "File uploaded successfully",
            "file_path": file_path,
            "file_info": file_info
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/download/{filename}")
async def download_file(filename: str):
    file_path = f"data/uploads/{filename}"
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="File not found")
    
    return FileResponse(file_path)

@router.get("/list")
async def list_files():
    upload_dir = "data/uploads"
    if not os.path.exists(upload_dir):
        return {"files": []}
    
    files = []
    for filename in os.listdir(upload_dir):
        file_path = os.path.join(upload_dir, filename)
        if os.path.isfile(file_path):
            file_info = get_file_info(file_path)
            files.append(file_info)
    
    return {"files": files}
