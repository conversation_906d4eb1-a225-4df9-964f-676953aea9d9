"""
HandWriter 完整功能测试脚本
"""
import requests
import time
import os

def test_api_health():
    """测试API健康状态"""
    try:
        response = requests.get("http://localhost:8000/")
        print(f"✅ API健康检查: {response.status_code}")
        print(f"响应: {response.json()}")
        return True
    except Exception as e:
        print(f"❌ API健康检查失败: {e}")
        return False

def test_api_docs():
    """测试API文档"""
    try:
        response = requests.get("http://localhost:8000/docs")
        print(f"✅ API文档访问: {response.status_code}")
        return True
    except Exception as e:
        print(f"❌ API文档访问失败: {e}")
        return False

def test_frontend():
    """测试前端服务"""
    try:
        response = requests.get("http://localhost:3000")
        print(f"✅ 前端服务访问: {response.status_code}")
        return True
    except Exception as e:
        print(f"❌ 前端服务访问失败: {e}")
        return False

def test_models_api():
    """测试模型列表API"""
    try:
        response = requests.get("http://localhost:8000/api/v1/handwriting/models")
        print(f"✅ 模型列表API: {response.status_code}")
        print(f"响应: {response.json()}")
        return True
    except Exception as e:
        print(f"❌ 模型列表API失败: {e}")
        return False

def test_file_upload_api():
    """测试文件上传API"""
    try:
        # 创建测试文件
        test_content = "这是测试文本内容"
        with open("test_printed.txt", "w", encoding="utf-8") as f:
            f.write(test_content)
        
        with open("test_handwritten.txt", "w", encoding="utf-8") as f:
            f.write(test_content)
        
        # 测试上传
        files = {
            'printed_text': open('test_printed.txt', 'rb'),
            'handwritten_text': open('test_handwritten.txt', 'rb')
        }
        
        response = requests.post(
            "http://localhost:8000/api/v1/handwriting/upload-samples",
            files=files
        )
        
        print(f"✅ 文件上传API测试: {response.status_code}")
        if response.status_code == 200:
            print(f"响应: {response.json()}")
        
        # 清理测试文件
        os.remove("test_printed.txt")
        os.remove("test_handwritten.txt")
        
        return True
    except Exception as e:
        print(f"❌ 文件上传API测试失败: {e}")
        return False

def test_text_generation_api():
    """测试文本生成API"""
    try:
        data = {
            "text": "测试文本",
            "output_format": "pdf"
        }
        
        response = requests.post(
            "http://localhost:8000/api/v1/handwriting/generate",
            json=data
        )
        
        print(f"✅ 文本生成API测试: {response.status_code}")
        return True
    except Exception as e:
        print(f"❌ 文本生成API测试失败: {e}")
        return False

def test_file_management_api():
    """测试文件管理API"""
    try:
        response = requests.get("http://localhost:8000/api/v1/files/list")
        print(f"✅ 文件列表API: {response.status_code}")
        print(f"响应: {response.json()}")
        return True
    except Exception as e:
        print(f"❌ 文件列表API失败: {e}")
        return False

def main():
    print("�� 开始HandWriter完整功能测试...")
    print("=" * 60)
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    time.sleep(3)
    
    # 测试API健康状态
    api_ok = test_api_health()
    
    if api_ok:
        # 测试API文档
        docs_ok = test_api_docs()
        
        # 测试前端服务
        frontend_ok = test_frontend()
        
        # 测试模型API
        models_ok = test_models_api()
        
        # 测试文件管理API
        files_ok = test_file_management_api()
        
        # 测试文件上传
        upload_ok = test_file_upload_api()
        
        # 测试文本生成
        generate_ok = test_text_generation_api()
        
        print("\n" + "=" * 60)
        print("📊 测试结果总结:")
        print(f"API健康状态: {'✅' if api_ok else '❌'}")
        print(f"API文档访问: {'✅' if docs_ok else '❌'}")
        print(f"前端服务访问: {'✅' if frontend_ok else '❌'}")
        print(f"模型列表API: {'✅' if models_ok else '❌'}")
        print(f"文件管理API: {'✅' if files_ok else '❌'}")
        print(f"文件上传功能: {'✅' if upload_ok else '❌'}")
        print(f"文本生成功能: {'✅' if generate_ok else '❌'}")
        
        success_count = sum([api_ok, docs_ok, frontend_ok, models_ok, files_ok, upload_ok, generate_ok])
        total_count = 7
        
        print(f"\n🎯 测试通过率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
        
        if success_count == total_count:
            print("\n🎉 所有功能测试通过！HandWriter应用运行正常。")
            print("\n📝 下一步可以:")
            print("1. 访问 http://localhost:3000 使用Web界面")
            print("2. 访问 http://localhost:8000/docs 查看API文档")
            print("3. 开始上传样本和生成手写体文本")
        else:
            print(f"\n⚠️ 有 {total_count - success_count} 项功能测试失败，请检查相关服务。")
    else:
        print("❌ API服务未启动，请先启动后端服务")

if __name__ == "__main__":
    main() 