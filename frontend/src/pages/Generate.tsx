import React, { useState } from 'react';
import axios from 'axios';

const Generate: React.FC = () => {
  const [text, setText] = useState('');
  const [outputFormat, setOutputFormat] = useState('pdf');
  const [isGenerating, setIsGenerating] = useState(false);
  const [downloadUrl, setDownloadUrl] = useState('');

  const handleGenerate = async () => {
    if (!text.trim()) {
      alert('请输入要生成的文本');
      return;
    }

    setIsGenerating(true);
    setDownloadUrl('');

    try {
      const response = await axios.post('/api/v1/handwriting/generate', {
        text: text,
        output_format: outputFormat
      }, {
        responseType: 'blob'
      });

      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response.data]));
      setDownloadUrl(url);
    } catch (error) {
      alert('生成失败，请重试');
      console.error('Generate error:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleDownload = () => {
    if (downloadUrl) {
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = `handwriting.${outputFormat}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent mb-4">生成手写体文本</h1>
        <p className="text-xl text-gray-600">输入文本内容，生成你的个性化手写体</p>
      </div>

      <div className="bg-gradient-to-br from-white to-green-50 p-8 rounded-2xl shadow-xl border border-green-200/50">
        <div className="mb-8">
          <label className="block text-lg font-semibold text-gray-800 mb-4">
            ✍️ 输入文本
          </label>
          <textarea
            value={text}
            onChange={(e) => setText(e.target.value)}
            placeholder="请输入要生成手写体的文本内容..."
            className="w-full h-40 px-6 py-4 border-2 border-green-200 rounded-xl focus:outline-none focus:ring-4 focus:ring-green-500/20 focus:border-green-500 transition-all duration-300 text-gray-700 placeholder-gray-400 shadow-inner bg-white/80"
          />
          {text && (
            <div className="mt-3 flex justify-between items-center">
              <div className="text-sm text-green-700 bg-green-100 px-3 py-1 rounded-full">
                字符数: {text.length}
              </div>
              <div className="text-sm text-gray-500">
                {text.length > 100 ? '📝 长文本' : text.length > 50 ? '📄 中等长度' : '📃 短文本'}
              </div>
            </div>
          )}
        </div>

        <div className="mb-8">
          <label className="block text-lg font-semibold text-gray-800 mb-4">
            📄 输出格式
          </label>
          <select
            value={outputFormat}
            onChange={(e) => setOutputFormat(e.target.value)}
            className="w-full px-6 py-4 border-2 border-green-200 rounded-xl focus:outline-none focus:ring-4 focus:ring-green-500/20 focus:border-green-500 transition-all duration-300 text-gray-700 bg-white/80 shadow-inner cursor-pointer"
          >
            <option value="pdf">📄 PDF文档</option>
            <option value="word">📝 Word文档</option>
            <option value="image">🖼️ 图片文件</option>
            <option value="gcode">🤖 G-code文件</option>
          </select>
        </div>

        <button
          onClick={handleGenerate}
          disabled={isGenerating || !text.trim()}
          className="w-full bg-gradient-to-r from-green-500 to-emerald-600 text-white py-4 px-8 rounded-xl hover:from-green-600 hover:to-emerald-700 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:hover:scale-100"
        >
          {isGenerating ? (
            <span className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              生成中...
            </span>
          ) : '✨ 生成手写体'}
        </button>

        {downloadUrl && (
          <button
            onClick={handleDownload}
            className="w-full mt-6 bg-gradient-to-r from-blue-500 to-indigo-600 text-white py-4 px-8 rounded-xl hover:from-blue-600 hover:to-indigo-700 font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
          >
            📥 下载文件
          </button>
        )}
      </div>

      <div className="mt-12 bg-gradient-to-br from-green-50 to-emerald-50 p-8 rounded-2xl border border-green-200/50 shadow-lg">
        <h3 className="text-2xl font-bold text-green-900 mb-6 flex items-center">
          <span className="text-3xl mr-3">💡</span>
          格式说明
        </h3>
        <div className="grid md:grid-cols-2 gap-4">
          <div className="bg-white/60 p-4 rounded-xl border border-green-200">
            <div className="flex items-center mb-2">
              <span className="text-2xl mr-2">📄</span>
              <strong className="text-green-800">PDF文档</strong>
            </div>
            <p className="text-green-700 text-sm">适合打印和分享</p>
          </div>
          <div className="bg-white/60 p-4 rounded-xl border border-green-200">
            <div className="flex items-center mb-2">
              <span className="text-2xl mr-2">📝</span>
              <strong className="text-green-800">Word文档</strong>
            </div>
            <p className="text-green-700 text-sm">可编辑的文档格式</p>
          </div>
          <div className="bg-white/60 p-4 rounded-xl border border-green-200">
            <div className="flex items-center mb-2">
              <span className="text-2xl mr-2">🖼️</span>
              <strong className="text-green-800">图片文件</strong>
            </div>
            <p className="text-green-700 text-sm">PNG格式，适合插入其他文档</p>
          </div>
          <div className="bg-white/60 p-4 rounded-xl border border-green-200">
            <div className="flex items-center mb-2">
              <span className="text-2xl mr-2">🤖</span>
              <strong className="text-green-800">G-code文件</strong>
            </div>
            <p className="text-green-700 text-sm">用于机器自动书写</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Generate;
