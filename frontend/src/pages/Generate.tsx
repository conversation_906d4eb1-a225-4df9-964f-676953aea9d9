import React, { useState } from 'react';
import axios from 'axios';

const Generate: React.FC = () => {
  const [text, setText] = useState('');
  const [outputFormat, setOutputFormat] = useState('pdf');
  const [isGenerating, setIsGenerating] = useState(false);
  const [downloadUrl, setDownloadUrl] = useState('');

  const handleGenerate = async () => {
    if (!text.trim()) {
      alert('请输入要生成的文本');
      return;
    }

    setIsGenerating(true);
    setDownloadUrl('');

    try {
      const response = await axios.post('/api/v1/handwriting/generate', {
        text: text,
        output_format: outputFormat
      }, {
        responseType: 'blob'
      });

      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response.data]));
      setDownloadUrl(url);
    } catch (error) {
      alert('生成失败，请重试');
      console.error('Generate error:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleDownload = () => {
    if (downloadUrl) {
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = `handwriting.${outputFormat}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      <h1 className="text-3xl font-bold text-gray-900 mb-8">生成手写体文本</h1>

      <div className="bg-white p-6 rounded-lg shadow-md">
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            输入文本
          </label>
          <textarea
            value={text}
            onChange={(e) => setText(e.target.value)}
            placeholder="请输入要生成手写体的文本内容..."
            className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          {text && (
            <div className="mt-2 text-sm text-gray-600">
              字符数: {text.length}
            </div>
          )}
        </div>

        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            输出格式
          </label>
          <select
            value={outputFormat}
            onChange={(e) => setOutputFormat(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="pdf">PDF文档</option>
            <option value="word">Word文档</option>
            <option value="image">图片文件</option>
            <option value="gcode">G-code文件</option>
          </select>
        </div>

        <button
          onClick={handleGenerate}
          disabled={isGenerating || !text.trim()}
          className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
        >
          {isGenerating ? '生成中...' : '生成手写体'}
        </button>

        {downloadUrl && (
          <button
            onClick={handleDownload}
            className="w-full mt-4 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700"
          >
            下载文件
          </button>
        )}
      </div>

      <div className="mt-8 bg-green-50 p-4 rounded-lg">
        <h3 className="font-semibold text-green-900 mb-2">💡 格式说明</h3>
        <ul className="text-sm text-green-800 space-y-1">
          <li>• <strong>PDF文档</strong>：适合打印和分享</li>
          <li>• <strong>Word文档</strong>：可编辑的文档格式</li>
          <li>• <strong>图片文件</strong>：PNG格式，适合插入其他文档</li>
          <li>• <strong>G-code文件</strong>：用于机器自动书写</li>
        </ul>
      </div>
    </div>
  );
};

export default Generate;
