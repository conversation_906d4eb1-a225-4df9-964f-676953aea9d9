import React, { useState, useEffect } from 'react';
import axios from 'axios';

const Settings: React.FC = () => {
  const [models, setModels] = useState<string[]>([]);
  const [selectedModel, setSelectedModel] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadModels();
  }, []);

  const loadModels = async () => {
    try {
      const response = await axios.get('/api/v1/handwriting/models');
      setModels(response.data.models);
      setIsLoading(false);
    } catch (error) {
      console.error('Failed to load models:', error);
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent mb-4">系统设置</h1>
        <p className="text-xl text-gray-600">配置模型参数和系统选项</p>
      </div>

      <div className="bg-gradient-to-br from-white to-purple-50 p-8 rounded-2xl shadow-xl border border-purple-200/50 mb-8">
        <h2 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
          <span className="text-3xl mr-3">🤖</span>
          模型管理
        </h2>
        
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <svg className="animate-spin h-8 w-8 text-purple-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span className="ml-3 text-purple-600 font-medium">加载中...</span>
          </div>
        ) : (
          <div>
            <label className="block text-lg font-semibold text-gray-800 mb-4">
              🎯 选择模型
            </label>
            <select
              value={selectedModel}
              onChange={(e) => setSelectedModel(e.target.value)}
              className="w-full px-6 py-4 border-2 border-purple-200 rounded-xl focus:outline-none focus:ring-4 focus:ring-purple-500/20 focus:border-purple-500 transition-all duration-300 text-gray-700 bg-white/80 shadow-inner cursor-pointer"
            >
              <option value="">请选择模型</option>
              {models.map((model) => (
                <option key={model} value={model}>
                  🤖 {model}
                </option>
              ))}
            </select>

            {models.length === 0 && (
              <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-xl">
                <div className="flex items-center">
                  <span className="text-2xl mr-3">⚠️</span>
                  <p className="text-yellow-800 font-medium">
                    暂无可用模型，请先上传样本进行训练
                  </p>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      <div className="bg-gradient-to-br from-white to-indigo-50 p-8 rounded-2xl shadow-xl border border-indigo-200/50">
        <h2 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
          <span className="text-3xl mr-3">📊</span>
          系统信息
        </h2>
        <div className="grid md:grid-cols-3 gap-6">
          <div className="bg-gradient-to-br from-blue-500 to-blue-600 p-6 rounded-xl text-white shadow-lg">
            <div className="text-2xl font-bold mb-2">v1.0.0</div>
            <div className="text-blue-100">系统版本</div>
          </div>
          <div className="bg-gradient-to-br from-green-500 to-emerald-600 p-6 rounded-xl text-white shadow-lg">
            <div className="text-2xl font-bold mb-2">🟢 运行中</div>
            <div className="text-green-100">服务状态</div>
          </div>
          <div className="bg-gradient-to-br from-purple-500 to-indigo-600 p-6 rounded-xl text-white shadow-lg">
            <div className="text-lg font-bold mb-2">:8000</div>
            <div className="text-purple-100">API端口</div>
          </div>
        </div>

        <div className="mt-8 p-6 bg-gray-50 rounded-xl border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">🔗 服务地址</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-white rounded-lg border">
              <span className="font-medium text-gray-700">前端界面</span>
              <code className="text-blue-600 bg-blue-50 px-3 py-1 rounded">http://localhost:3000</code>
            </div>
            <div className="flex items-center justify-between p-3 bg-white rounded-lg border">
              <span className="font-medium text-gray-700">API接口</span>
              <code className="text-green-600 bg-green-50 px-3 py-1 rounded">http://localhost:8000</code>
            </div>
            <div className="flex items-center justify-between p-3 bg-white rounded-lg border">
              <span className="font-medium text-gray-700">API文档</span>
              <code className="text-purple-600 bg-purple-50 px-3 py-1 rounded">http://localhost:8000/docs</code>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;
