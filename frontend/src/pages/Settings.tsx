import React, { useState, useEffect } from 'react';
import axios from 'axios';

const Settings: React.FC = () => {
  const [models, setModels] = useState<string[]>([]);
  const [selectedModel, setSelectedModel] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadModels();
  }, []);

  const loadModels = async () => {
    try {
      const response = await axios.get('/api/v1/handwriting/models');
      setModels(response.data.models);
      setIsLoading(false);
    } catch (error) {
      console.error('Failed to load models:', error);
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      <h1 className="text-3xl font-bold text-gray-900 mb-8">设置</h1>

      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">模型管理</h2>
        
        {isLoading ? (
          <p className="text-gray-600">加载中...</p>
        ) : (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              选择模型
            </label>
            <select
              value={selectedModel}
              onChange={(e) => setSelectedModel(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">请选择模型</option>
              {models.map((model) => (
                <option key={model} value={model}>
                  {model}
                </option>
              ))}
            </select>
            
            {models.length === 0 && (
              <p className="mt-2 text-sm text-gray-600">
                暂无可用模型，请先上传样本进行训练
              </p>
            )}
          </div>
        )}
      </div>

      <div className="mt-6 bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">系统信息</h2>
        <div className="space-y-2 text-sm text-gray-600">
          <p>版本：1.0.0</p>
          <p>状态：运行中</p>
          <p>API地址：http://localhost:8000</p>
        </div>
      </div>
    </div>
  );
};

export default Settings;
