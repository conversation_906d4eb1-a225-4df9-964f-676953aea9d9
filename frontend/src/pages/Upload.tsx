import React, { useState } from 'react';
import axios from 'axios';

const Upload: React.FC = () => {
  const [printedFile, setPrintedFile] = useState<File | null>(null);
  const [handwrittenFile, setHandwrittenFile] = useState<File | null>(null);
  const [printedPreview, setPrintedPreview] = useState<string>('');
  const [handwrittenPreview, setHandwrittenPreview] = useState<string>('');
  const [isUploading, setIsUploading] = useState(false);
  const [uploadResult, setUploadResult] = useState<string>('');

  const handlePrintedFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      setPrintedFile(file);

      // 创建预览
      const reader = new FileReader();
      reader.onload = (e) => {
        setPrintedPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleHandwrittenFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      setHandwrittenFile(file);

      // 创建预览
      const reader = new FileReader();
      reader.onload = (e) => {
        setHandwrittenPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleUpload = async () => {
    if (!printedFile || !handwrittenFile) {
      alert('请选择印刷体和手写体文件');
      return;
    }

    setIsUploading(true);
    setUploadResult('');

    try {
      const formData = new FormData();
      formData.append('printed_text', printedFile);
      formData.append('handwritten_text', handwrittenFile);

      const response = await axios.post('/api/v1/handwriting/upload-samples', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      setUploadResult('样本上传成功！模型训练完成。');
    } catch (error) {
      setUploadResult('上传失败，请重试。');
      console.error('Upload error:', error);
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">上传样本</h1>
        <p className="text-xl text-gray-600">上传印刷体和手写体样本，让AI学习你的书写风格</p>
      </div>

      <div className="bg-gradient-to-br from-white to-blue-50 p-8 rounded-2xl shadow-xl border border-blue-200/50">
        <div className="mb-8">
          <label className="block text-lg font-semibold text-gray-800 mb-4">
            📄 印刷体文件
          </label>
          <input
            type="file"
            accept="image/*"
            onChange={handlePrintedFileChange}
            className="block w-full text-sm text-gray-600 file:mr-4 file:py-3 file:px-6 file:rounded-xl file:border-0 file:text-sm file:font-semibold file:bg-gradient-to-r file:from-blue-500 file:to-blue-600 file:text-white hover:file:from-blue-600 hover:file:to-blue-700 file:shadow-lg hover:file:shadow-xl file:transition-all file:duration-300 cursor-pointer"
          />
          {printedFile && (
            <div className="mt-4 p-4 bg-blue-50 rounded-xl border border-blue-200">
              <p className="text-sm font-medium text-blue-800 mb-2">✅ 已选择: {printedFile.name}</p>
              {printedPreview && (
                <img
                  src={printedPreview}
                  alt="印刷体预览"
                  className="mt-3 max-w-full h-40 object-contain border-2 border-blue-200 rounded-xl shadow-md"
                />
              )}
            </div>
          )}
        </div>

        <div className="mb-8">
          <label className="block text-lg font-semibold text-gray-800 mb-4">
            ✍️ 手写体文件
          </label>
          <input
            type="file"
            accept="image/*"
            onChange={handleHandwrittenFileChange}
            className="block w-full text-sm text-gray-600 file:mr-4 file:py-3 file:px-6 file:rounded-xl file:border-0 file:text-sm file:font-semibold file:bg-gradient-to-r file:from-green-500 file:to-emerald-600 file:text-white hover:file:from-green-600 hover:file:to-emerald-700 file:shadow-lg hover:file:shadow-xl file:transition-all file:duration-300 cursor-pointer"
          />
          {handwrittenFile && (
            <div className="mt-4 p-4 bg-green-50 rounded-xl border border-green-200">
              <p className="text-sm font-medium text-green-800 mb-2">✅ 已选择: {handwrittenFile.name}</p>
              {handwrittenPreview && (
                <img
                  src={handwrittenPreview}
                  alt="手写体预览"
                  className="mt-3 max-w-full h-40 object-contain border-2 border-green-200 rounded-xl shadow-md"
                />
              )}
            </div>
          )}
        </div>

        <button
          onClick={handleUpload}
          disabled={isUploading || !printedFile || !handwrittenFile}
          className="w-full bg-gradient-to-r from-purple-500 to-indigo-600 text-white py-4 px-8 rounded-xl hover:from-purple-600 hover:to-indigo-700 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:hover:scale-100"
        >
          {isUploading ? (
            <span className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              上传中...
            </span>
          ) : '🚀 上传样本'}
        </button>

        {uploadResult && (
          <div className={`mt-6 p-6 rounded-xl shadow-lg ${
            uploadResult.includes('成功')
              ? 'bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 text-green-800'
              : 'bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 text-red-800'
          }`}>
            <div className="flex items-center">
              <span className="text-2xl mr-3">
                {uploadResult.includes('成功') ? '🎉' : '❌'}
              </span>
              <span className="font-medium">{uploadResult}</span>
            </div>
          </div>
        )}
      </div>

      <div className="mt-12 bg-gradient-to-br from-blue-50 to-indigo-50 p-8 rounded-2xl border border-blue-200/50 shadow-lg">
        <h3 className="text-2xl font-bold text-blue-900 mb-6 flex items-center">
          <span className="text-3xl mr-3">📝</span>
          使用说明
        </h3>
        <ul className="text-blue-800 space-y-4">
          <li className="flex items-start">
            <span className="text-blue-500 mr-3 mt-1">•</span>
            <span className="font-medium">请确保印刷体和手写体文件包含相同的文本内容</span>
          </li>
          <li className="flex items-start">
            <span className="text-blue-500 mr-3 mt-1">•</span>
            <span className="font-medium">支持 JPG、PNG、BMP 等常见图像格式</span>
          </li>
          <li className="flex items-start">
            <span className="text-blue-500 mr-3 mt-1">•</span>
            <span className="font-medium">图像质量越高，训练效果越好</span>
          </li>
          <li className="flex items-start">
            <span className="text-blue-500 mr-3 mt-1">•</span>
            <span className="font-medium">上传后系统将自动开始模型训练</span>
          </li>
        </ul>
      </div>
    </div>
  );
};

export default Upload;
