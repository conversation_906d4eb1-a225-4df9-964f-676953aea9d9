import React, { useState } from 'react';
import axios from 'axios';

const Upload: React.FC = () => {
  const [printedFile, setPrintedFile] = useState<File | null>(null);
  const [handwrittenFile, setHandwrittenFile] = useState<File | null>(null);
  const [printedPreview, setPrintedPreview] = useState<string>('');
  const [handwrittenPreview, setHandwrittenPreview] = useState<string>('');
  const [isUploading, setIsUploading] = useState(false);
  const [uploadResult, setUploadResult] = useState<string>('');

  const handlePrintedFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      setPrintedFile(file);

      // 创建预览
      const reader = new FileReader();
      reader.onload = (e) => {
        setPrintedPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleHandwrittenFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      setHandwrittenFile(file);

      // 创建预览
      const reader = new FileReader();
      reader.onload = (e) => {
        setHandwrittenPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleUpload = async () => {
    if (!printedFile || !handwrittenFile) {
      alert('请选择印刷体和手写体文件');
      return;
    }

    setIsUploading(true);
    setUploadResult('');

    try {
      const formData = new FormData();
      formData.append('printed_text', printedFile);
      formData.append('handwritten_text', handwrittenFile);

      const response = await axios.post('/api/v1/handwriting/upload-samples', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      setUploadResult('样本上传成功！模型训练完成。');
    } catch (error) {
      setUploadResult('上传失败，请重试。');
      console.error('Upload error:', error);
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      <h1 className="text-3xl font-bold text-gray-900 mb-8">上传样本</h1>

      <div className="bg-white p-6 rounded-lg shadow-md">
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            印刷体文件
          </label>
          <input
            type="file"
            accept="image/*"
            onChange={handlePrintedFileChange}
            className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          />
          {printedFile && (
            <div className="mt-2">
              <p className="text-sm text-gray-600">已选择: {printedFile.name}</p>
              {printedPreview && (
                <img
                  src={printedPreview}
                  alt="印刷体预览"
                  className="mt-2 max-w-full h-32 object-contain border rounded"
                />
              )}
            </div>
          )}
        </div>

        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            手写体文件
          </label>
          <input
            type="file"
            accept="image/*"
            onChange={handleHandwrittenFileChange}
            className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          />
          {handwrittenFile && (
            <div className="mt-2">
              <p className="text-sm text-gray-600">已选择: {handwrittenFile.name}</p>
              {handwrittenPreview && (
                <img
                  src={handwrittenPreview}
                  alt="手写体预览"
                  className="mt-2 max-w-full h-32 object-contain border rounded"
                />
              )}
            </div>
          )}
        </div>

        <button
          onClick={handleUpload}
          disabled={isUploading || !printedFile || !handwrittenFile}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
        >
          {isUploading ? '上传中...' : '上传样本'}
        </button>

        {uploadResult && (
          <div className={`mt-4 p-4 rounded-md ${
            uploadResult.includes('成功') ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
          }`}>
            {uploadResult}
          </div>
        )}
      </div>

      <div className="mt-8 bg-blue-50 p-4 rounded-lg">
        <h3 className="font-semibold text-blue-900 mb-2">📝 使用说明</h3>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• 请确保印刷体和手写体文件包含相同的文本内容</li>
          <li>• 支持 JPG、PNG、BMP 等常见图像格式</li>
          <li>• 图像质量越高，训练效果越好</li>
          <li>• 上传后系统将自动开始模型训练</li>
        </ul>
      </div>
    </div>
  );
};

export default Upload;
