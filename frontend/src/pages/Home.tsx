import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';

const Home: React.FC = () => {
  const [apiStatus, setApiStatus] = useState<'loading' | 'online' | 'offline'>('loading');
  const [stats, setStats] = useState({
    models: 0,
    files: 0
  });

  useEffect(() => {
    checkApiStatus();
    loadStats();
  }, []);

  const checkApiStatus = async () => {
    try {
      const response = await axios.get('/api/v1/handwriting/models');
      setApiStatus('online');
    } catch (error) {
      setApiStatus('offline');
    }
  };

  const loadStats = async () => {
    try {
      const [modelsResponse, filesResponse] = await Promise.all([
        axios.get('/api/v1/handwriting/models'),
        axios.get('/api/v1/files/list')
      ]);

      setStats({
        models: modelsResponse.data.models.length,
        files: filesResponse.data.files.length
      });
    } catch (error) {
      console.error('加载统计信息失败:', error);
    }
  };

  return (
    <div className="max-w-6xl mx-auto">
      {/* 欢迎区域 */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          欢迎使用 HandWriter
        </h1>
        <p className="text-xl text-gray-600 mb-8">
          基于AI的手写体模仿软件，让机器学会你的笔迹
        </p>

        {/* API状态指示器 */}
        <div className="flex justify-center items-center space-x-2 mb-8">
          <div className={`w-3 h-3 rounded-full ${
            apiStatus === 'online' ? 'bg-green-500' :
            apiStatus === 'offline' ? 'bg-red-500' : 'bg-yellow-500'
          }`}></div>
          <span className="text-sm text-gray-600">
            服务状态: {
              apiStatus === 'online' ? '在线' :
              apiStatus === 'offline' ? '离线' : '检查中...'
            }
          </span>
        </div>
      </div>

      {/* 功能卡片 */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
        <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
          <div className="text-3xl mb-4">📤</div>
          <h3 className="text-xl font-semibold mb-2">上传样本</h3>
          <p className="text-gray-600 mb-4">
            上传印刷体和手写体样本，让AI学习你的书写风格
          </p>
          <Link
            to="/upload"
            className="inline-block bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
          >
            开始上传
          </Link>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
          <div className="text-3xl mb-4">✍️</div>
          <h3 className="text-xl font-semibold mb-2">生成文本</h3>
          <p className="text-gray-600 mb-4">
            输入文本内容，生成你的个性化手写体
          </p>
          <Link
            to="/generate"
            className="inline-block bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition-colors"
          >
            生成文本
          </Link>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
          <div className="text-3xl mb-4">⚙️</div>
          <h3 className="text-xl font-semibold mb-2">系统设置</h3>
          <p className="text-gray-600 mb-4">
            配置模型参数和输出格式选项
          </p>
          <Link
            to="/settings"
            className="inline-block bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600 transition-colors"
          >
            打开设置
          </Link>
        </div>
      </div>

      {/* 统计信息 */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-semibold mb-4">系统概览</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-500">{stats.models}</div>
            <div className="text-sm text-gray-600">训练模型</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-500">{stats.files}</div>
            <div className="text-sm text-gray-600">上传文件</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-500">4</div>
            <div className="text-sm text-gray-600">输出格式</div>
          </div>
          <div className="text-center">
            <div className={`text-3xl font-bold ${apiStatus === 'online' ? 'text-green-500' : 'text-red-500'}`}>
              {apiStatus === 'online' ? '✓' : '✗'}
            </div>
            <div className="text-sm text-gray-600">服务状态</div>
          </div>
        </div>
      </div>

      {/* 快速开始指南 */}
      <div className="mt-12 bg-blue-50 rounded-lg p-6">
        <h2 className="text-2xl font-semibold mb-4">快速开始</h2>
        <div className="space-y-4">
          <div className="flex items-start space-x-3">
            <div className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">1</div>
            <div>
              <h3 className="font-semibold">上传样本文件</h3>
              <p className="text-gray-600">准备印刷体和对应的手写体样本，上传到系统进行学习</p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <div className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">2</div>
            <div>
              <h3 className="font-semibold">训练AI模型</h3>
              <p className="text-gray-600">系统会自动分析你的书写特征并训练个性化模型</p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <div className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">3</div>
            <div>
              <h3 className="font-semibold">生成手写体</h3>
              <p className="text-gray-600">输入任意文本，生成你的个性化手写体文档</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;