import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';

const Home: React.FC = () => {
  const [apiStatus, setApiStatus] = useState<'loading' | 'online' | 'offline'>('loading');
  const [stats, setStats] = useState({
    models: 0,
    files: 0
  });

  useEffect(() => {
    checkApiStatus();
    loadStats();
  }, []);

  const checkApiStatus = async () => {
    try {
      const response = await axios.get('/api/v1/handwriting/models');
      setApiStatus('online');
    } catch (error) {
      setApiStatus('offline');
    }
  };

  const loadStats = async () => {
    try {
      const [modelsResponse, filesResponse] = await Promise.all([
        axios.get('/api/v1/handwriting/models'),
        axios.get('/api/v1/files/list')
      ]);

      setStats({
        models: modelsResponse.data.models.length,
        files: filesResponse.data.files.length
      });
    } catch (error) {
      console.error('加载统计信息失败:', error);
    }
  };

  return (
    <div className="max-w-6xl mx-auto">
      {/* 欢迎区域 */}
      <div className="text-center mb-16">
        <h1 className="text-5xl font-bold mb-6 text-blue-600">
          🎨 欢迎使用 HandWriter
        </h1>
        <p className="text-xl text-gray-700 mb-8 max-w-2xl mx-auto">
          基于AI的手写体模仿软件，让机器学会你的笔迹，创造独一无二的个性化文档
        </p>

        {/* API状态指示器 */}
        <div className="inline-flex items-center space-x-3 bg-white px-6 py-3 rounded-full shadow-lg border border-gray-200">
          <div className={`w-3 h-3 rounded-full ${
            apiStatus === 'online' ? 'bg-green-500' :
            apiStatus === 'offline' ? 'bg-red-500' : 'bg-yellow-500'
          }`}></div>
          <span className="text-sm font-medium text-gray-700">
            服务状态: {
              apiStatus === 'online' ? '🟢 在线' :
              apiStatus === 'offline' ? '🔴 离线' : '🟡 检查中...'
            }
          </span>
        </div>
      </div>

      {/* 功能卡片 */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
        <div className="bg-blue-50 rounded-2xl shadow-lg p-8 hover:shadow-xl transition-shadow border border-blue-200">
          <div className="text-4xl mb-6">📤</div>
          <h3 className="text-2xl font-bold mb-3 text-gray-800">上传样本</h3>
          <p className="text-gray-600 mb-6">
            上传印刷体和手写体样本，让AI学习你的书写风格
          </p>
          <Link
            to="/upload"
            className="inline-flex items-center justify-center bg-blue-500 text-white px-6 py-3 rounded-xl hover:bg-blue-600 transition-colors font-medium shadow-lg"
          >
            开始上传 →
          </Link>
        </div>

        <div className="bg-green-50 rounded-2xl shadow-lg p-8 hover:shadow-xl transition-shadow border border-green-200">
          <div className="text-4xl mb-6">✍️</div>
          <h3 className="text-2xl font-bold mb-3 text-gray-800">生成文本</h3>
          <p className="text-gray-600 mb-6">
            输入文本内容，生成你的个性化手写体
          </p>
          <Link
            to="/generate"
            className="inline-flex items-center justify-center bg-green-500 text-white px-6 py-3 rounded-xl hover:bg-green-600 transition-colors font-medium shadow-lg"
          >
            生成文本 →
          </Link>
        </div>

        <div className="bg-purple-50 rounded-2xl shadow-lg p-8 hover:shadow-xl transition-shadow border border-purple-200">
          <div className="text-4xl mb-6">⚙️</div>
          <h3 className="text-2xl font-bold mb-3 text-gray-800">系统设置</h3>
          <p className="text-gray-600 mb-6">
            配置模型参数和输出格式选项
          </p>
          <Link
            to="/settings"
            className="inline-flex items-center justify-center bg-purple-500 text-white px-6 py-3 rounded-xl hover:bg-purple-600 transition-colors font-medium shadow-lg"
          >
            打开设置 →
          </Link>
        </div>
      </div>

      {/* 统计信息 */}
      <div className="bg-gradient-to-r from-white to-gray-50 rounded-2xl shadow-xl p-8 border border-gray-200/50">
        <h2 className="text-3xl font-bold mb-8 text-center bg-gradient-to-r from-gray-700 to-gray-900 bg-clip-text text-transparent">系统概览</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          <div className="text-center bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl p-6 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
            <div className="text-4xl font-bold mb-2">{stats.models}</div>
            <div className="text-blue-100 font-medium">训练模型</div>
          </div>
          <div className="text-center bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl p-6 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
            <div className="text-4xl font-bold mb-2">{stats.files}</div>
            <div className="text-green-100 font-medium">上传文件</div>
          </div>
          <div className="text-center bg-gradient-to-br from-purple-500 to-violet-600 rounded-xl p-6 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
            <div className="text-4xl font-bold mb-2">4</div>
            <div className="text-purple-100 font-medium">输出格式</div>
          </div>
          <div className="text-center bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl p-6 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
            <div className={`text-4xl font-bold mb-2`}>
              {apiStatus === 'online' ? '✓' : '✗'}
            </div>
            <div className="text-indigo-100 font-medium">服务状态</div>
          </div>
        </div>
      </div>

      {/* 快速开始指南 */}
      <div className="mt-16 bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50 rounded-2xl p-8 border border-indigo-200/50 shadow-xl">
        <h2 className="text-3xl font-bold mb-8 text-center bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">快速开始</h2>
        <div className="space-y-6">
          <div className="flex items-start space-x-4 group">
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-full w-10 h-10 flex items-center justify-center text-lg font-bold shadow-lg group-hover:scale-110 transition-transform duration-300">1</div>
            <div className="flex-1">
              <h3 className="text-xl font-bold text-gray-800 mb-2">上传样本文件</h3>
              <p className="text-gray-600 leading-relaxed">准备印刷体和对应的手写体样本，上传到系统进行学习</p>
            </div>
          </div>
          <div className="flex items-start space-x-4 group">
            <div className="bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-full w-10 h-10 flex items-center justify-center text-lg font-bold shadow-lg group-hover:scale-110 transition-transform duration-300">2</div>
            <div className="flex-1">
              <h3 className="text-xl font-bold text-gray-800 mb-2">训练AI模型</h3>
              <p className="text-gray-600 leading-relaxed">系统会自动分析你的书写特征并训练个性化模型</p>
            </div>
          </div>
          <div className="flex items-start space-x-4 group">
            <div className="bg-gradient-to-r from-purple-500 to-violet-600 text-white rounded-full w-10 h-10 flex items-center justify-center text-lg font-bold shadow-lg group-hover:scale-110 transition-transform duration-300">3</div>
            <div className="flex-1">
              <h3 className="text-xl font-bold text-gray-800 mb-2">生成手写体</h3>
              <p className="text-gray-600 leading-relaxed">输入任意文本，生成你的个性化手写体文档</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;