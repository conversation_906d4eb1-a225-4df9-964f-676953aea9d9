{"name": "handwriter-frontend", "version": "1.0.0", "description": "HandWriter前端应用", "main": "index.js", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:3000 && electron .\""}, "dependencies": {"@headlessui/react": "^1.7.0", "@heroicons/react": "^2.0.0", "autoprefixer": "^10.4.21", "axios": "^1.6.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "tailwindcss": "^3.3.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.0.0", "concurrently": "^8.0.0", "electron": "^27.0.0", "typescript": "^5.0.0", "vite": "^4.5.0", "wait-on": "^7.0.0"}}